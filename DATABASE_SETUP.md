# Database Setup Guide

This guide explains how to set up PostgreSQL databases for the Travel Booking Platform microservices.

## 🏗️ Database Architecture

The platform uses **two PostgreSQL databases**:

### 1. `vendor_central` Database
**Used by Vendor Central Services:**
- **Vendor Service** (Port 8080) - Vendor registration and management
- **Listing Service** (Port 8081) - Trip listings and availability

**Tables:**
- `vendors` - Vendor profiles and authentication
- `trips` - Trip information and details
- `trip_images` - Trip photos and media
- `trip_availabilities` - Available dates and booking slots

### 2. `mall` Database
**Used by Mall Services:**
- **User Service** (Port 8082) - User registration and authentication
- **Feed Service** (Port 8083) - Recommendations and search
- **Detail Page Service** (Port 8084) - Trip details, ratings, and bookings

**Tables:**
- `users` - User profiles and authentication
- `user_preferences` - User travel preferences for recommendations
- `user_activities` - User activity tracking for personalization
- `ratings` - Trip ratings and reviews
- `bookings` - Trip bookings and reservations

## 🚀 Quick Start

### Option 1: Using Docker (Recommended)

1. **Start PostgreSQL with Docker:**
```bash
# Start PostgreSQL container
docker run -d \
  --name travel-booking-postgres \
  -e POSTGRES_USER=postgres \
  -e POSTGRES_PASSWORD=postgres \
  -e POSTGRES_DB=postgres \
  -p 5432:5432 \
  -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" \
  postgres:15-alpine
```

2. **Verify setup:**
```bash
# Check if databases were created
docker exec travel-booking-postgres psql -U postgres -c "\l" | grep -E "(mall|vendor_central)"
```

### Option 2: Using Docker Compose

1. **Start PostgreSQL:**
```bash
docker-compose up postgres -d
```

2. **Check logs:**
```bash
docker-compose logs postgres
```

### Option 3: Using Local PostgreSQL

1. **Install PostgreSQL** (if not already installed):
```bash
# macOS with Homebrew
brew install postgresql
brew services start postgresql

# Ubuntu/Debian
sudo apt-get install postgresql postgresql-contrib
sudo systemctl start postgresql

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo systemctl start postgresql
```

2. **Create databases manually:**
```bash
# Test connection
./scripts/test-db-connection.sh

# Or manually:
psql -U postgres -c "CREATE DATABASE mall;"
psql -U postgres -c "CREATE DATABASE vendor_central;"
```

3. **Run initialization script:**
```bash
psql -U postgres -f scripts/init-db.sql
```

## 🧪 Testing the Setup

### Test Database Connection
```bash
./scripts/test-db-connection.sh
```

### Test Vendor Service with Database
```bash
./scripts/test-vendor-service.sh
```

### Manual Testing
```bash
# Connect to vendor_central database
psql -U postgres -d vendor_central

# Connect to mall database  
psql -U postgres -d mall

# List all tables
\dt

# Check vendor table structure
\d vendors
```

## 🔧 Configuration

### Environment Variables

Copy `.env.example` to `.env` and adjust as needed:

```bash
cp .env.example .env
```

**Database Configuration:**
```env
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres

# Service-specific database names
VENDOR_DB_NAME=vendor_central
LISTING_DB_NAME=vendor_central
USER_DB_NAME=mall
FEED_DB_NAME=mall
DETAIL_PAGE_DB_NAME=mall
```

### Service Configuration

Each service reads database configuration from environment variables:

- `DB_HOST` - Database host (default: localhost)
- `DB_PORT` - Database port (default: 5432)
- `DB_USER` - Database user (default: postgres)
- `DB_PASSWORD` - Database password (default: postgres)
- `DB_NAME` - Database name (service-specific)

## 📊 Database Schema

### Vendor Central Schema (`vendor_central`)

```sql
-- Vendors table
CREATE TABLE vendors (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    company_name VARCHAR(200),
    contact_name VARCHAR(100),
    phone_number VARCHAR(20) NOT NULL,
    -- ... other vendor fields
    is_verified BOOLEAN DEFAULT FALSE,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Trips table
CREATE TABLE trips (
    id UUID PRIMARY KEY,
    vendor_id UUID NOT NULL REFERENCES vendors(id),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    destination VARCHAR(200) NOT NULL,
    duration_days INTEGER NOT NULL,
    max_participants INTEGER NOT NULL,
    price_per_person DECIMAL(10,2) NOT NULL,
    -- ... other trip fields
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### Mall Schema (`mall`)

```sql
-- Users table
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    first_name VARCHAR(100),
    last_name VARCHAR(100),
    -- ... other user fields
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Bookings table
CREATE TABLE bookings (
    id UUID PRIMARY KEY,
    user_id UUID NOT NULL,
    trip_id UUID NOT NULL,
    vendor_id UUID NOT NULL,
    booking_date DATE NOT NULL,
    number_of_people INTEGER NOT NULL DEFAULT 1,
    total_amount DECIMAL(10,2) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🔍 Troubleshooting

### Common Issues

1. **Connection refused:**
   - Check if PostgreSQL is running: `pg_isready -h localhost -p 5432`
   - Verify port 5432 is not blocked by firewall

2. **Database does not exist:**
   - Run: `./scripts/test-db-connection.sh`
   - Or manually create: `psql -U postgres -c "CREATE DATABASE vendor_central;"`

3. **Permission denied:**
   - Check PostgreSQL user permissions
   - Ensure password is correct (default: postgres)

4. **Service won't start:**
   - Check environment variables are set correctly
   - Verify database connection with `psql`
   - Check service logs for specific errors

### Useful Commands

```bash
# Check PostgreSQL status
pg_isready -h localhost -p 5432 -U postgres

# List databases
psql -U postgres -c "\l"

# Connect to specific database
psql -U postgres -d vendor_central

# Check table structure
psql -U postgres -d vendor_central -c "\d vendors"

# View recent logs (Docker)
docker logs travel-booking-postgres

# Stop PostgreSQL (Docker)
docker stop travel-booking-postgres
```

## 🚀 Next Steps

1. **Start the database** using one of the methods above
2. **Test the vendor service** with `./scripts/test-vendor-service.sh`
3. **Implement other services** following the same pattern
4. **Add sample data** for testing and development
5. **Set up monitoring** and backup strategies for production
