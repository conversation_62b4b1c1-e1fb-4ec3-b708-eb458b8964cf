# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres

# Vendor Central Services
VENDOR_DB_NAME=vendor_central
LISTING_DB_NAME=vendor_central

# Mall Services  
USER_DB_NAME=mall
FEED_DB_NAME=mall
DETAIL_PAGE_DB_NAME=mall

# Service Ports
VENDOR_PORT=8080
LISTING_PORT=8081
USER_PORT=8082
FEED_PORT=8083
DETAIL_PAGE_PORT=8084

# JWT Configuration (for user service)
JWT_SECRET=your_jwt_secret_key_change_in_production
JWT_EXPIRY=24h

# Elasticsearch Configuration (for feed service)
ES_HOST=localhost
ES_PORT=9200
