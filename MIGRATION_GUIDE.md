# Migration Guide: From Monolith to Modular Architecture

This guide explains how to migrate from the old monolithic structure to the new modular architecture.

## Overview of Changes

### Before (Monolithic)
```
backend/
├── cmd/api/main.go          # Single entry point
├── handlers/                # All HTTP handlers
├── models/                  # All models
├── services/                # All services
└── utils/                   # Utilities
```

### After (Modular)
```
backend/
├── cmd/
│   ├── vendor-central/      # B2B service
│   └── mall/               # B2C service
├── shared/                 # Shared utilities
├── vendor-central/         # B2B modules
│   ├── vendor-onboarding/
│   └── listing/
└── mall/                   # B2C modules
    ├── user-registration/
    ├── feed/
    └── detail-page/
```

## Migration Steps

### 1. Code Mapping

#### Old → New Module Mapping

| Old Component | New Module | Service |
|---------------|------------|---------|
| `models.Provider` | `vendor-onboarding/models.Vendor` | Vendor Central |
| `models.Trip` | `listing/models.Trip` | Vendor Central |
| `models.User` | `user-registration/models.User` | Mall |
| `models.Rating` | `detail-page/models.Rating` | Mall |
| `handlers.TripHandler` | Split across multiple modules | Both |
| `services.ElasticsearchService` | `feed/` module | Mall |

#### Handler Migration

**Old TripHandler methods:**
- `CreateTrip` → `vendor-central/listing/interface/http_handler.go`
- `GetTrip` → `mall/detail-page/interface/http_handler.go`
- `SearchTrips` → `mall/feed/interface/http_handler.go`
- `RateTrip` → `mall/detail-page/interface/http_handler.go`

### 2. Database Schema Changes

#### Vendor Central Database
```sql
-- Vendor onboarding
CREATE TABLE vendors (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    password_hash VARCHAR NOT NULL,
    company_name VARCHAR,
    -- ... other vendor fields
);

-- Listing
CREATE TABLE trips (
    id UUID PRIMARY KEY,
    vendor_id UUID REFERENCES vendors(id),
    title VARCHAR NOT NULL,
    -- ... other trip fields
);
```

#### Mall Database
```sql
-- User registration
CREATE TABLE users (
    id UUID PRIMARY KEY,
    email VARCHAR UNIQUE NOT NULL,
    password_hash VARCHAR NOT NULL,
    -- ... other user fields
);

-- Detail page
CREATE TABLE ratings (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    trip_id UUID,
    rate DECIMAL,
    -- ... other rating fields
);
```

### 3. API Endpoint Changes

#### Vendor Central (B2B) - Port 8080
```
POST /api/v1/vendors/register          # Vendor registration
GET  /api/v1/vendors/{id}              # Get vendor
PUT  /api/v1/vendors/{id}/verify       # Verify vendor
POST /api/v1/trips                     # Create trip (vendor)
PUT  /api/v1/trips/{id}                # Update trip (vendor)
```

#### Mall (B2C) - Port 8081
```
POST /api/v1/users/register            # User registration
POST /api/v1/users/login               # User login
GET  /api/v1/feed                      # Get trip feed
GET  /api/v1/trips/{id}                # Get trip details
POST /api/v1/trips/{id}/ratings        # Rate trip
POST /api/v1/trips/{id}/bookings       # Book trip
```

### 4. Environment Variables

#### New Environment Variables
```bash
# Service Ports
VENDOR_CENTRAL_PORT=8080
MALL_PORT=8081

# Database (shared)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=travel_booking

# Elasticsearch (for mall service)
ES_HOST=localhost
ES_PORT=9200
```

### 5. Running the New Architecture

#### Development
```bash
# Run both services
./scripts/run-services.sh

# Or run individually
go run cmd/vendor-central/main.go
go run cmd/mall/main.go
```

#### Production
```bash
# Build
go build -o vendor-central ./cmd/vendor-central
go build -o mall ./cmd/mall

# Run
./vendor-central &
./mall &
```

## Implementation Status

### ✅ Completed
- [x] Shared infrastructure (config, database, utils)
- [x] Vendor Central - Vendor Onboarding module (complete)
- [x] Module structure for all components
- [x] Clean architecture interfaces
- [x] Database migration setup
- [x] Build and deployment scripts

### 🚧 In Progress / TODO
- [ ] Vendor Central - Listing module implementation
- [ ] Mall - User Registration module implementation
- [ ] Mall - Feed module implementation
- [ ] Mall - Detail Page module implementation
- [ ] Inter-module communication
- [ ] Authentication middleware
- [ ] API documentation

## Testing the Migration

### 1. Test Vendor Registration (Vendor Central)
```bash
curl -X POST http://localhost:8080/api/v1/vendors/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "company_name": "Test Travel Company",
    "contact_name": "John Doe",
    "phone_number": "+**********",
    "address_line_1": "123 Main St",
    "city": "New York",
    "country": "USA"
  }'
```

### 2. Test Health Endpoints
```bash
# Vendor Central
curl http://localhost:8080/health

# Mall
curl http://localhost:8081/health
```

## Rollback Plan

If issues arise, you can rollback by:

1. **Keep the old code**: The old monolithic code is preserved
2. **Database backup**: Ensure you have database backups
3. **Gradual migration**: Implement modules one by one
4. **Feature flags**: Use feature flags to switch between old and new implementations

## Benefits of New Architecture

### 1. Separation of Concerns
- B2B and B2C concerns are separated
- Each module has a single responsibility
- Clear boundaries between components

### 2. Scalability
- Services can be scaled independently
- Modules can be deployed separately
- Database can be partitioned by service

### 3. Maintainability
- Easier to understand and modify
- Reduced coupling between components
- Clear interfaces and contracts

### 4. Team Organization
- Teams can work on different modules independently
- Clear ownership of components
- Reduced merge conflicts

## Next Steps

1. **Complete module implementations**: Finish implementing all modules
2. **Add authentication**: Implement JWT-based authentication
3. **Add monitoring**: Add logging, metrics, and health checks
4. **Add tests**: Write comprehensive tests for each module
5. **Documentation**: Create API documentation for each service
6. **Deployment**: Set up CI/CD for the new architecture

## Support

For questions about the migration:
1. Check this guide first
2. Review the module README files
3. Check the interface definitions
4. Ask the development team
