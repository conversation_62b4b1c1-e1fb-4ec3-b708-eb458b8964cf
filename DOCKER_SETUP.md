# Docker Setup and Integration Tests

This document describes the Docker setup for all microservices and the comprehensive integration testing framework for the vendor service.

## 🐳 Docker Architecture

### Service Dockerfiles

Each service has its own optimized Dockerfile with:
- **Multi-stage builds** for smaller final images
- **Non-root user** for security
- **Health checks** for monitoring
- **Alpine Linux** base for minimal footprint

**Created Dockerfiles:**
- `services/vendor-central/vendor/Dockerfile`
- `services/vendor-central/listing/Dockerfile`
- `services/mall/user/Dockerfile`
- `services/mall/feed/Dockerfile`
- `services/mall/detail-page/Dockerfile`

### Docker Compose Configuration

The `docker-compose.yml` includes:
- **PostgreSQL database** with initialization scripts
- **All 5 microservices** with proper dependencies
- **Health checks** and service dependencies
- **Network isolation** with custom bridge network
- **Volume persistence** for database data

## 🧪 Integration Testing Framework

### Vendor Service Integration Tests

**Location:** `services/vendor-central/vendor/tests/`

**Test Files:**
- `integration_test.go` - Comprehensive API integration tests
- `test_helper.go` - Test utilities and database management

**Test Coverage:**
- ✅ Health check endpoint
- ✅ Vendor registration (valid data)
- ✅ Duplicate email rejection
- ✅ Get vendor by ID
- ✅ Get vendor by ID (not found)
- ✅ List pending vendors
- ✅ Invalid request handling
- ✅ Database connectivity and transactions

### Test Features

**Database Management:**
- Automatic test database setup
- Schema migration for tests
- Data cleanup between tests
- Connection pooling and timeout handling

**HTTP Testing:**
- Full HTTP server simulation
- Real API endpoint testing
- JSON request/response validation
- Error handling verification

**Test Isolation:**
- Each test runs in isolation
- Database cleanup between tests
- No test interdependencies
- Parallel test execution support

## 🚀 Usage Instructions

### 1. Start All Services with Docker

```bash
# Start all services
./scripts/start-all-services.sh

# Or start individual services
./scripts/start-service.sh vendor-service
./scripts/start-service.sh postgres vendor-service
```

### 2. Start Individual Services

```bash
# Start specific services
docker-compose up -d postgres vendor-service
docker-compose up -d postgres listing-service
docker-compose up -d postgres user-service feed-service detail-page-service
```

### 3. Run Integration Tests

```bash
# Run integration tests (requires PostgreSQL)
./scripts/run-integration-tests.sh

# Or run tests manually
cd services/vendor-central/vendor
go test -v ./tests/... -timeout=30s
```

### 4. Test Docker Deployment

```bash
# Test vendor service in Docker
./scripts/test-vendor-docker.sh

# This script:
# - Starts PostgreSQL and vendor service in Docker
# - Waits for services to be ready
# - Runs comprehensive API tests
# - Validates database connectivity
# - Cleans up containers
```

## 🔧 Configuration

### Environment Variables

**Database Configuration:**
```env
DB_HOST=postgres          # Docker service name
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=vendor_central    # or mall
```

**Service Ports:**
- PostgreSQL: 5432
- Vendor Service: 8080
- Listing Service: 8081
- User Service: 8082
- Feed Service: 8083
- Detail Page Service: 8084

### Test Configuration

**Test Environment Variables:**
```env
TEST_DB_HOST=localhost
TEST_DB_PORT=5432
TEST_DB_USER=postgres
TEST_DB_PASSWORD=postgres
TEST_DB_NAME=vendor_central
```

## 📊 Service Health Checks

Each service includes health check endpoints:

```bash
# Check service health
curl http://localhost:8080/health  # Vendor Service
curl http://localhost:8081/health  # Listing Service
curl http://localhost:8082/health  # User Service
curl http://localhost:8083/health  # Feed Service
curl http://localhost:8084/health  # Detail Page Service
```

## 🧪 API Testing Examples

### Vendor Registration

```bash
curl -X POST http://localhost:8080/api/v1/vendors/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "company_name": "Test Company",
    "contact_name": "John Doe",
    "phone_number": "+**********",
    "address_line_1": "123 Main St",
    "city": "New York",
    "country": "USA"
  }'
```

### Get Vendor by ID

```bash
curl http://localhost:8080/api/v1/vendors/{vendor-id}
```

### List Pending Vendors

```bash
curl http://localhost:8080/api/v1/vendors/pending
```

## 🔍 Troubleshooting

### Common Issues

1. **Docker not running:**
   ```bash
   # Check Docker status
   docker info
   # Start Docker Desktop or Docker daemon
   ```

2. **Port conflicts:**
   ```bash
   # Check what's using the ports
   lsof -i :5432
   lsof -i :8080
   ```

3. **Database connection issues:**
   ```bash
   # Check PostgreSQL logs
   docker-compose logs postgres
   
   # Test database connection
   docker-compose exec postgres psql -U postgres -d vendor_central
   ```

4. **Service build failures:**
   ```bash
   # Check service logs
   docker-compose logs vendor-service
   
   # Rebuild services
   docker-compose build --no-cache vendor-service
   ```

### Useful Commands

```bash
# View all running containers
docker-compose ps

# View service logs
docker-compose logs -f vendor-service

# Execute commands in containers
docker-compose exec postgres psql -U postgres
docker-compose exec vendor-service /bin/sh

# Stop all services
docker-compose down

# Remove all containers and volumes
docker-compose down -v

# Rebuild and restart services
docker-compose up --build
```

## 🎯 Next Steps

1. **Run the integration tests** to verify the vendor service works correctly
2. **Test Docker deployment** with the provided scripts
3. **Implement similar tests** for other services
4. **Add monitoring and logging** for production deployment
5. **Set up CI/CD pipelines** for automated testing and deployment

## 📚 Additional Resources

- [Docker Compose Documentation](https://docs.docker.com/compose/)
- [Go Testing Documentation](https://golang.org/pkg/testing/)
- [GORM Documentation](https://gorm.io/docs/)
- [Chi Router Documentation](https://github.com/go-chi/chi)
