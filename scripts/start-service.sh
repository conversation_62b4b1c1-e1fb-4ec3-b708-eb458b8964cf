#!/bin/bash

# Start individual service using Docker Compose
if [ $# -eq 0 ]; then
    echo "Usage: $0 <service-name>"
    echo ""
    echo "Available services:"
    echo "  postgres         - PostgreSQL database"
    echo "  vendor-service   - Vendor registration and management"
    echo "  listing-service  - Trip listing and availability"
    echo "  user-service     - User registration and authentication"
    echo "  feed-service     - Trip recommendations and search"
    echo "  detail-page-service - Trip details, ratings, and bookings"
    echo ""
    echo "Examples:"
    echo "  $0 postgres"
    echo "  $0 vendor-service"
    echo "  $0 vendor-service listing-service"
    exit 1
fi

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    docker-compose down
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Start the specified services
echo "🚀 Starting services: $@"

# Always start postgres first if not already running
if ! docker-compose ps postgres | grep -q "Up"; then
    echo "🐘 Starting PostgreSQL first..."
    docker-compose up -d postgres
    
    # Wait for PostgreSQL to be ready
    echo "⏳ Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
            echo "✅ PostgreSQL is ready!"
            break
        fi
        echo "⏳ Still waiting... ($i/30)"
        sleep 2
    done
fi

# Start the requested services
echo "🔨 Building and starting requested services..."
docker-compose up --build "$@"
