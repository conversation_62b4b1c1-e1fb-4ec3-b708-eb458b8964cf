#!/bin/bash

# Start PostgreSQL database for development using Docker Compose
echo "🐘 Starting PostgreSQL database with Docker Compose..."

# Check if Docker is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Stop existing services
echo "🧹 Stopping existing services..."
docker-compose down

# Start only PostgreSQL
echo "🚀 Starting PostgreSQL with Docker Compose..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting... ($i/30)"
    sleep 2
done

# Verify databases were created
echo "🔍 Verifying databases..."
docker-compose exec postgres psql -U postgres -c "\l" | grep -E "(mall|vendor_central)"

echo ""
echo "🎉 PostgreSQL is running!"
echo "📊 Connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   User: postgres"
echo "   Password: postgres"
echo "   Databases: mall, vendor_central"
echo ""
echo "🛠️  To connect manually:"
echo "   docker-compose exec postgres psql -U postgres -d mall"
echo "   docker-compose exec postgres psql -U postgres -d vendor_central"
echo ""
echo "🛑 To stop:"
echo "   docker-compose down"
