#!/bin/bash

# Start PostgreSQL database for development
echo "🐘 Starting PostgreSQL database..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Stop and remove existing container if it exists
echo "🧹 Cleaning up existing containers..."
docker stop travel-booking-postgres 2>/dev/null || true
docker rm travel-booking-postgres 2>/dev/null || true

# Start PostgreSQL container
echo "🚀 Starting PostgreSQL container..."
docker run -d \
    --name travel-booking-postgres \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" \
    postgres:15-alpine

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
sleep 5

# Check if PostgreSQL is ready
for i in {1..30}; do
    if docker exec travel-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting... ($i/30)"
    sleep 2
done

# Verify databases were created
echo "🔍 Verifying databases..."
docker exec travel-booking-postgres psql -U postgres -c "\l" | grep -E "(mall|vendor_central)"

echo ""
echo "🎉 PostgreSQL is running!"
echo "📊 Connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   User: postgres"
echo "   Password: postgres"
echo "   Databases: mall, vendor_central"
echo ""
echo "🛠️  To connect manually:"
echo "   docker exec -it travel-booking-postgres psql -U postgres -d mall"
echo "   docker exec -it travel-booking-postgres psql -U postgres -d vendor_central"
echo ""
echo "🛑 To stop:"
echo "   docker stop travel-booking-postgres"
