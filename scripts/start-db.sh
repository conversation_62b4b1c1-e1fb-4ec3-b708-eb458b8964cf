#!/bin/bash

# Start PostgreSQL database for development using Podman
echo "🐘 Starting PostgreSQL database with <PERSON><PERSON>..."

# Check if <PERSON><PERSON> is available
if ! command -v podman > /dev/null 2>&1; then
    echo "❌ Podman is not installed. Please install <PERSON>dman first."
    echo "💡 Install with: brew install podman (macOS) or follow https://podman.io/getting-started/installation"
    exit 1
fi

# Stop and remove existing container if it exists
echo "🧹 Cleaning up existing containers..."
podman stop travel-booking-postgres 2>/dev/null || true
podman rm travel-booking-postgres 2>/dev/null || true

# Start PostgreSQL container
echo "🚀 Starting PostgreSQL with <PERSON>dman..."
podman run -d \
    --name travel-booking-postgres \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:Z" \
    postgres:15-alpine

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if podman exec travel-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting... ($i/30)"
    sleep 2
done

# Create databases if they don't exist
echo "🏗️  Creating databases..."
podman exec travel-booking-postgres psql -U postgres -c "CREATE DATABASE mall;" 2>/dev/null || echo "Database 'mall' already exists"
podman exec travel-booking-postgres psql -U postgres -c "CREATE DATABASE vendor_central;" 2>/dev/null || echo "Database 'vendor_central' already exists"

# Verify databases were created
echo "🔍 Verifying databases..."
podman exec travel-booking-postgres psql -U postgres -c "\l" | grep -E "(mall|vendor_central)"

echo ""
echo "🎉 PostgreSQL is running!"
echo "📊 Connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   User: postgres"
echo "   Password: postgres"
echo "   Databases: mall, vendor_central"
echo ""
echo "🛠️  To connect manually:"
echo "   podman exec -it travel-booking-postgres psql -U postgres -d mall"
echo "   podman exec -it travel-booking-postgres psql -U postgres -d vendor_central"
echo ""
echo "🛑 To stop:"
echo "   podman stop travel-booking-postgres"
