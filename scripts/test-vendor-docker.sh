#!/bin/bash

# Test vendor service running in Podman with integration tests
echo "🐳 Testing Vendor Service with <PERSON>dman..."

# Check if <PERSON><PERSON> is available
if ! command -v podman > /dev/null 2>&1; then
    echo "❌ Podman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up Podman containers..."
    podman stop travel-booking-postgres vendor-service 2>/dev/null || true
    podman rm travel-booking-postgres vendor-service 2>/dev/null || true
    echo "✅ Cleanup complete"
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Stop any existing containers
echo "🧹 Stopping existing containers..."
podman stop travel-booking-postgres vendor-service 2>/dev/null || true
podman rm travel-booking-postgres vendor-service 2>/dev/null || true

# Start PostgreSQL first
echo "🚀 Starting PostgreSQL..."
podman run -d \
    --name travel-booking-postgres \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    postgres:15-alpine

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if podman exec travel-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting for PostgreSQL... ($i/30)"
    sleep 2
done

# Create databases
echo "🏗️  Creating databases..."
podman exec travel-booking-postgres psql -U postgres -c "CREATE DATABASE vendor_central;" 2>/dev/null || echo "Database already exists"

# Build and start vendor service
echo "🔨 Building vendor service..."
cd services/vendor-central/vendor
podman build -t vendor-service -f Dockerfile ../../..
cd ../../..

echo "🚀 Starting Vendor Service..."
podman run -d \
    --name vendor-service \
    -e DB_HOST=localhost \
    -e DB_PORT=5432 \
    -e DB_USER=postgres \
    -e DB_PASSWORD=postgres \
    -e DB_NAME=vendor_central \
    -e PORT=8080 \
    -p 8080:8080 \
    --network host \
    vendor-service

# Wait for vendor service to be ready
echo "⏳ Waiting for Vendor Service to be ready..."
for i in {1..30}; do
    if curl -s http://localhost:8080/health > /dev/null 2>&1; then
        echo "✅ Vendor Service is ready!"
        break
    fi
    echo "⏳ Still waiting for Vendor Service... ($i/30)"
    sleep 2
done

# Check if services are ready
if ! curl -s http://localhost:8080/health > /dev/null 2>&1; then
    echo "❌ Vendor Service failed to start"
    echo "📋 Checking service logs..."
    podman logs vendor-service
    exit 1
fi

# Test health endpoint
echo ""
echo "🏥 Testing health endpoint..."
HEALTH_RESPONSE=$(curl -s http://localhost:8080/health)
echo "Response: $HEALTH_RESPONSE"

if [ "$HEALTH_RESPONSE" != "Vendor Service OK" ]; then
    echo "❌ Health check failed"
    exit 1
fi

# Test vendor registration
echo ""
echo "👤 Testing vendor registration..."
VENDOR_DATA='{
    "email": "<EMAIL>",
    "password": "password123",
    "company_name": "Docker Test Company",
    "contact_name": "Docker User",
    "phone_number": "+**********",
    "address_line_1": "123 Docker St",
    "city": "Container City",
    "country": "Dockerland",
    "description": "A test company running in Docker"
}'

REGISTER_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$VENDOR_DATA" \
    http://localhost:8080/api/v1/vendors/register)

echo "Registration Response: $REGISTER_RESPONSE"

# Extract vendor ID from response
VENDOR_ID=$(echo "$REGISTER_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$VENDOR_ID" ]; then
    echo "❌ Failed to register vendor"
    exit 1
fi

echo "✅ Vendor registered with ID: $VENDOR_ID"

# Test getting vendor by ID
echo ""
echo "🔍 Testing get vendor by ID..."
GET_RESPONSE=$(curl -s http://localhost:8080/api/v1/vendors/$VENDOR_ID)
echo "Get Vendor Response: $GET_RESPONSE"

# Verify the response contains the vendor data
if echo "$GET_RESPONSE" | grep -q "Docker Test Company"; then
    echo "✅ Get vendor by ID successful"
else
    echo "❌ Get vendor by ID failed"
    exit 1
fi

# Test listing pending vendors
echo ""
echo "📋 Testing list pending vendors..."
LIST_RESPONSE=$(curl -s http://localhost:8080/api/v1/vendors/pending)
echo "List Pending Response: $LIST_RESPONSE"

# Verify the response contains our vendor
if echo "$LIST_RESPONSE" | grep -q "Docker Test Company"; then
    echo "✅ List pending vendors successful"
else
    echo "❌ List pending vendors failed"
    exit 1
fi

# Test duplicate email registration
echo ""
echo "🔄 Testing duplicate email registration..."
DUPLICATE_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$VENDOR_DATA" \
    http://localhost:8080/api/v1/vendors/register)

echo "Duplicate Registration Response: $DUPLICATE_RESPONSE"

# Should fail with error message
if echo "$DUPLICATE_RESPONSE" | grep -q "error"; then
    echo "✅ Duplicate email registration correctly rejected"
else
    echo "❌ Duplicate email registration should have failed"
    exit 1
fi

# Test invalid requests
echo ""
echo "❌ Testing invalid requests..."

# Test invalid JSON
INVALID_JSON_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "invalid json" \
    http://localhost:8080/api/v1/vendors/register)

echo "Invalid JSON Response: $INVALID_JSON_RESPONSE"

# Test missing required fields
INCOMPLETE_DATA='{"email": "<EMAIL>"}'
INCOMPLETE_RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$INCOMPLETE_DATA" \
    http://localhost:8080/api/v1/vendors/register)

echo "Incomplete Data Response: $INCOMPLETE_RESPONSE"

# Test invalid UUID
INVALID_UUID_RESPONSE=$(curl -s http://localhost:8080/api/v1/vendors/invalid-uuid)
echo "Invalid UUID Response: $INVALID_UUID_RESPONSE"

# Check database state
echo ""
echo "🗄️  Checking database state..."
VENDOR_COUNT=$(podman exec travel-booking-postgres psql -U postgres -d vendor_central -t -c "SELECT COUNT(*) FROM vendors;")
echo "Total vendors in database: $VENDOR_COUNT"

# Show service logs
echo ""
echo "📋 Service logs (last 20 lines):"
podman logs --tail=20 vendor-service

echo ""
echo "🎉 All Podman tests completed successfully!"
echo "=========================================="
echo ""
echo "📊 Test Summary:"
echo "   ✅ Health check"
echo "   ✅ Vendor registration"
echo "   ✅ Get vendor by ID"
echo "   ✅ List pending vendors"
echo "   ✅ Duplicate email rejection"
echo "   ✅ Invalid request handling"
echo "   ✅ Database connectivity"
echo ""
echo "🐳 Services tested:"
echo "   - PostgreSQL (vendor_central database)"
echo "   - Vendor Service (Podman container)"
echo ""
echo "🔗 Service URLs:"
echo "   - Health: http://localhost:8080/health"
echo "   - API: http://localhost:8080/api/v1/vendors"
