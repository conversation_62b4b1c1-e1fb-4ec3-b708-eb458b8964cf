#!/bin/bash

# Verify Docker setup and integration tests
echo "🔍 Verifying Docker Setup and Integration Tests..."
echo "=================================================="

# Check if required files exist
echo ""
echo "📁 Checking required files..."

FILES=(
    "docker-compose.yml"
    "services/vendor-central/vendor/Dockerfile"
    "services/vendor-central/listing/Dockerfile"
    "services/mall/user/Dockerfile"
    "services/mall/feed/Dockerfile"
    "services/mall/detail-page/Dockerfile"
    "services/vendor-central/vendor/tests/integration_test.go"
    "services/vendor-central/vendor/tests/test_helper.go"
    "scripts/start-all-services.sh"
    "scripts/start-service.sh"
    "scripts/run-integration-tests.sh"
    "scripts/test-vendor-docker.sh"
)

for file in "${FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "✅ $file"
    else
        echo "❌ $file (missing)"
    fi
done

# Check if Docker is available
echo ""
echo "🐳 Checking Docker..."
if command -v docker > /dev/null 2>&1; then
    echo "✅ Docker is installed"
    if docker info > /dev/null 2>&1; then
        echo "✅ Docker is running"
    else
        echo "❌ Docker is not running"
    fi
else
    echo "❌ Docker is not installed"
fi

# Check if docker-compose is available
if command -v docker-compose > /dev/null 2>&1; then
    echo "✅ docker-compose is installed"
else
    echo "❌ docker-compose is not installed"
fi

# Check Go installation
echo ""
echo "🔧 Checking Go..."
if command -v go > /dev/null 2>&1; then
    GO_VERSION=$(go version)
    echo "✅ Go is installed: $GO_VERSION"
else
    echo "❌ Go is not installed"
fi

# Check vendor service dependencies
echo ""
echo "📦 Checking vendor service dependencies..."
cd services/vendor-central/vendor
if [ -f "go.mod" ]; then
    echo "✅ go.mod exists"
    if grep -q "github.com/stretchr/testify" go.mod; then
        echo "✅ testify dependency found"
    else
        echo "❌ testify dependency missing"
    fi
    if grep -q "gorm.io/gorm" go.mod; then
        echo "✅ GORM dependency found"
    else
        echo "❌ GORM dependency missing"
    fi
else
    echo "❌ go.mod not found"
fi
cd ../../..

# Check script permissions
echo ""
echo "🔐 Checking script permissions..."
SCRIPTS=(
    "scripts/start-all-services.sh"
    "scripts/start-service.sh"
    "scripts/run-integration-tests.sh"
    "scripts/test-vendor-docker.sh"
    "scripts/start-db.sh"
)

for script in "${SCRIPTS[@]}"; do
    if [ -x "$script" ]; then
        echo "✅ $script (executable)"
    else
        echo "❌ $script (not executable)"
        echo "   Fix with: chmod +x $script"
    fi
done

# Validate docker-compose.yml
echo ""
echo "📋 Validating docker-compose.yml..."
if command -v docker-compose > /dev/null 2>&1; then
    if docker-compose config > /dev/null 2>&1; then
        echo "✅ docker-compose.yml is valid"
    else
        echo "❌ docker-compose.yml has errors"
        echo "   Check with: docker-compose config"
    fi
else
    echo "⚠️  Cannot validate docker-compose.yml (docker-compose not available)"
fi

# Check integration test structure
echo ""
echo "🧪 Checking integration test structure..."
if [ -f "services/vendor-central/vendor/tests/integration_test.go" ]; then
    TEST_FUNCTIONS=$(grep -c "func (suite \*VendorIntegrationTestSuite)" services/vendor-central/vendor/tests/integration_test.go)
    echo "✅ Integration test file exists with $TEST_FUNCTIONS test functions"
else
    echo "❌ Integration test file missing"
fi

# Summary
echo ""
echo "📊 Setup Summary"
echo "================"
echo ""
echo "🐳 Docker Components:"
echo "   - PostgreSQL database with init scripts"
echo "   - 5 microservice Dockerfiles"
echo "   - Complete docker-compose.yml"
echo "   - Health checks and dependencies"
echo ""
echo "🧪 Testing Framework:"
echo "   - Comprehensive integration tests"
echo "   - Database test utilities"
echo "   - API endpoint testing"
echo "   - Error handling validation"
echo ""
echo "🚀 Deployment Scripts:"
echo "   - Start all services"
echo "   - Start individual services"
echo "   - Run integration tests"
echo "   - Test Docker deployment"
echo ""
echo "📚 Documentation:"
echo "   - DOCKER_SETUP.md"
echo "   - DATABASE_SETUP.md"
echo "   - Updated README.md"
echo ""
echo "🎯 Next Steps:"
echo "1. Ensure Docker is running"
echo "2. Run: ./scripts/start-db.sh"
echo "3. Run: ./scripts/run-integration-tests.sh"
echo "4. Run: ./scripts/test-vendor-docker.sh"
echo ""
echo "✅ Setup verification complete!"
