#!/bin/bash

# Test API endpoints to verify database connectivity
echo "🧪 Testing API endpoints..."

BASE_URL="http://localhost:8080"

# Check if service is running
echo "🔍 Checking if vendor service is running..."
if ! curl -s "$BASE_URL/health" > /dev/null; then
    echo "❌ Vendor service is not running on $BASE_URL"
    echo "💡 Start it with: ./scripts/test-vendor-service.sh"
    exit 1
fi

echo "✅ Vendor service is running!"

# Test health endpoint
echo ""
echo "🏥 Testing health endpoint..."
curl -s "$BASE_URL/health"
echo ""

# Test vendor registration
echo ""
echo "👤 Testing vendor registration..."
VENDOR_DATA='{
    "email": "<EMAIL>",
    "password": "password123",
    "company_name": "Test Travel Company",
    "contact_name": "<PERSON>",
    "phone_number": "+**********",
    "address_line_1": "123 Main St",
    "city": "New York",
    "country": "USA",
    "description": "A test travel company"
}'

RESPONSE=$(curl -s -X POST \
    -H "Content-Type: application/json" \
    -d "$VENDOR_DATA" \
    "$BASE_URL/api/v1/vendors/register")

echo "Response: $RESPONSE"

# Extract vendor ID from response (if successful)
VENDOR_ID=$(echo "$RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -n "$VENDOR_ID" ]; then
    echo "✅ Vendor created with ID: $VENDOR_ID"
    
    # Test getting vendor by ID
    echo ""
    echo "🔍 Testing get vendor by ID..."
    curl -s "$BASE_URL/api/v1/vendors/$VENDOR_ID" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/v1/vendors/$VENDOR_ID"
    echo ""
    
    # Test listing pending vendors
    echo ""
    echo "📋 Testing list pending vendors..."
    curl -s "$BASE_URL/api/v1/vendors/pending" | jq '.' 2>/dev/null || curl -s "$BASE_URL/api/v1/vendors/pending"
    echo ""
else
    echo "❌ Failed to create vendor"
fi

echo ""
echo "🎉 API testing complete!"
echo ""
echo "💡 You can also test manually with:"
echo "   curl $BASE_URL/health"
echo "   curl -X POST -H 'Content-Type: application/json' -d '$VENDOR_DATA' $BASE_URL/api/v1/vendors/register"
