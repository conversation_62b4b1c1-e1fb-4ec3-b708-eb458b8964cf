#!/bin/bash

# Create directories
mkdir -p api/swagger-ui

# Download Swagger UI
SWAGGER_UI_VERSION="5.9.0"
SWAGGER_UI_URL="https://github.com/swagger-api/swagger-ui/archive/v${SWAGGER_UI_VERSION}.tar.gz"

echo "Downloading Swagger UI ${SWAGGER_UI_VERSION}..."
curl -sL ${SWAGGER_UI_URL} | tar xz

echo "Setting up Swagger UI..."
cp -r swagger-ui-${SWAGGER_UI_VERSION}/dist/* api/swagger-ui/

# Create custom index.html with our OpenAPI spec
cat > api/swagger-ui/index.html << 'EOF'
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <title>Travel Booking API Documentation</title>
    <link rel="stylesheet" type="text/css" href="./swagger-ui.css" />
    <link rel="stylesheet" type="text/css" href="./index.css" />
    <link rel="icon" type="image/png" href="./favicon-32x32.png" sizes="32x32" />
    <link rel="icon" type="image/png" href="./favicon-16x16.png" sizes="16x16" />
  </head>

  <body>
    <div id="swagger-ui"></div>
    <script src="./swagger-ui-bundle.js" charset="UTF-8"> </script>
    <script src="./swagger-ui-standalone-preset.js" charset="UTF-8"> </script>
    <script>
    window.onload = function() {
      window.ui = SwaggerUIBundle({
        url: "/openapi.yaml",
        dom_id: '#swagger-ui',
        deepLinking: true,
        presets: [
          SwaggerUIBundle.presets.apis,
          SwaggerUIStandalonePreset
        ],
        plugins: [
          SwaggerUIBundle.plugins.DownloadUrl
        ],
        layout: "StandaloneLayout"
      });
    };
    </script>
  </body>
</html>
EOF

# Clean up
rm -rf swagger-ui-${SWAGGER_UI_VERSION}

echo "Swagger UI setup complete!"
echo "Run 'go run cmd/apidocs/main.go' to start the API documentation server"