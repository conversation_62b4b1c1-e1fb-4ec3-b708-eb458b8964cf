#!/bin/bash

# Complete development environment setup
echo "🚀 Setting up Travel Booking Platform Development Environment"
echo "============================================================"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to wait for PostgreSQL to be ready
wait_for_postgres() {
    echo "⏳ Waiting for PostgreSQL to be ready..."
    for i in {1..30}; do
        if pg_isready -h localhost -p 5432 -U postgres > /dev/null 2>&1; then
            echo "✅ PostgreSQL is ready!"
            return 0
        fi
        echo "⏳ Still waiting... ($i/30)"
        sleep 2
    done
    echo "❌ PostgreSQL failed to start within 60 seconds"
    return 1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    echo "💡 Visit: https://docs.docker.com/get-docker/"
    exit 1
fi

if ! command_exists go; then
    echo "❌ Go is not installed. Please install Go first."
    echo "💡 Visit: https://golang.org/doc/install"
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Start PostgreSQL
echo ""
echo "🐘 Starting PostgreSQL database..."

# Stop existing container if running
docker stop travel-booking-postgres 2>/dev/null || true
docker rm travel-booking-postgres 2>/dev/null || true

# Start PostgreSQL container
docker run -d \
    --name travel-booking-postgres \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" \
    postgres:15-alpine

if ! wait_for_postgres; then
    echo "❌ Failed to start PostgreSQL"
    exit 1
fi

# Verify databases were created
echo "🔍 Verifying databases..."
docker exec travel-booking-postgres psql -U postgres -c "\l" | grep -E "(mall|vendor_central)"

# Build services
echo ""
echo "🔨 Building services..."

# Build vendor service
echo "📦 Building vendor service..."
cd services/vendor-central/vendor
if go build -o ../../../bin/vendor ./cmd; then
    echo "✅ Vendor service built successfully"
else
    echo "❌ Failed to build vendor service"
    exit 1
fi
cd ../../..

# Build other services (basic ones)
echo "📦 Building listing service..."
cd services/vendor-central/listing
if go build -o ../../../bin/listing ./cmd; then
    echo "✅ Listing service built successfully"
else
    echo "❌ Failed to build listing service"
    exit 1
fi
cd ../../..

echo "📦 Building user service..."
cd services/mall/user
if go build -o ../../../bin/user ./cmd; then
    echo "✅ User service built successfully"
else
    echo "❌ Failed to build user service"
    exit 1
fi
cd ../../..

echo "📦 Building feed service..."
cd services/mall/feed
if go build -o ../../../bin/feed ./cmd; then
    echo "✅ Feed service built successfully"
else
    echo "❌ Failed to build feed service"
    exit 1
fi
cd ../../..

echo "📦 Building detail-page service..."
cd services/mall/detail-page
if go build -o ../../../bin/detail-page ./cmd; then
    echo "✅ Detail-page service built successfully"
else
    echo "❌ Failed to build detail-page service"
    exit 1
fi
cd ../../..

# Create environment file
echo ""
echo "⚙️  Creating environment configuration..."
if [ ! -f .env ]; then
    cp .env.example .env
    echo "✅ Created .env file from .env.example"
else
    echo "ℹ️  .env file already exists"
fi

# Summary
echo ""
echo "🎉 Development environment setup complete!"
echo "=========================================="
echo ""
echo "📊 Database Information:"
echo "   Host: localhost:5432"
echo "   User: postgres"
echo "   Password: postgres"
echo "   Databases: mall, vendor_central"
echo ""
echo "🏗️  Built Services:"
echo "   ✅ vendor-service (bin/vendor)"
echo "   ✅ listing-service (bin/listing)"
echo "   ✅ user-service (bin/user)"
echo "   ✅ feed-service (bin/feed)"
echo "   ✅ detail-page-service (bin/detail-page)"
echo ""
echo "🚀 Quick Start Commands:"
echo "   # Test vendor service with database"
echo "   ./scripts/test-vendor-service.sh"
echo ""
echo "   # Test API endpoints"
echo "   ./scripts/test-api.sh"
echo ""
echo "   # Run all services"
echo "   ./scripts/run-services.sh"
echo ""
echo "🛑 To stop PostgreSQL:"
echo "   docker stop travel-booking-postgres"
echo ""
echo "📚 For more information, see DATABASE_SETUP.md"
