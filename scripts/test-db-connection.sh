#!/bin/bash

# Test database connection and setup
echo "🔍 Testing database connection..."

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 -U postgres > /dev/null 2>&1; then
    echo "❌ PostgreSQL is not running on localhost:5432"
    echo "💡 Please start PostgreSQL first:"
    echo "   - Using Docker: docker run -d --name postgres -e POSTGRES_PASSWORD=postgres -p 5432:5432 postgres:15-alpine"
    echo "   - Using Homebrew: brew services start postgresql"
    echo "   - Using system service: sudo systemctl start postgresql"
    exit 1
fi

echo "✅ PostgreSQL is running!"

# Test connection and create databases
echo "🏗️  Creating databases..."

# Create databases if they don't exist
PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -c "CREATE DATABASE mall;" 2>/dev/null || echo "Database 'mall' already exists or error occurred"
PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -c "CREATE DATABASE vendor_central;" 2>/dev/null || echo "Database 'vendor_central' already exists or error occurred"

# Verify databases exist
echo "📊 Verifying databases..."
PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -c "\l" | grep -E "(mall|vendor_central)"

echo ""
echo "🎉 Database setup complete!"
echo "📋 Available databases:"
echo "   - mall (for user, feed, detail-page services)"
echo "   - vendor_central (for vendor, listing services)"
echo ""
echo "🔗 Connection details:"
echo "   Host: localhost"
echo "   Port: 5432"
echo "   User: postgres"
echo "   Password: postgres"
