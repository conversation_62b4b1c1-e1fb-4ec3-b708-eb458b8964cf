#!/bin/bash

# Test vendor service with database connection
echo "🧪 Testing Vendor Service with Database..."

# Set environment variables
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=postgres
export DB_NAME=vendor_central
export PORT=8080

# Check if PostgreSQL is running
if ! pg_isready -h localhost -p 5432 -U postgres > /dev/null 2>&1; then
    echo "❌ PostgreSQL is not running. Please start it first."
    exit 1
fi

# Create database if it doesn't exist
echo "🏗️  Ensuring database exists..."
PGPASSWORD=postgres psql -h localhost -p 5432 -U postgres -c "CREATE DATABASE vendor_central;" 2>/dev/null || echo "Database already exists"

# Build and run vendor service
echo "🔨 Building vendor service..."
cd services/vendor-central/vendor

if go build -o ../../../bin/vendor ./cmd; then
    echo "✅ Build successful!"
    echo "🚀 Starting vendor service..."
    echo "📡 Service will be available at http://localhost:8080"
    echo "🔍 Health check: http://localhost:8080/health"
    echo "📋 API endpoints:"
    echo "   POST /api/v1/vendors/register - Register new vendor"
    echo "   GET  /api/v1/vendors/{id} - Get vendor by ID"
    echo "   GET  /api/v1/vendors/pending - List pending vendors"
    echo ""
    echo "🛑 Press Ctrl+C to stop the service"
    echo ""
    
    # Run the service
    ../../../bin/vendor
else
    echo "❌ Build failed!"
    exit 1
fi
