#!/bin/bash

# Script to run all microservices for development

set -e

echo "🚀 Building and running all microservices..."

# Create bin directory if it doesn't exist
mkdir -p bin

# Function to cleanup background processes
cleanup() {
    echo "🛑 Stopping all microservices..."
    kill $VENDOR_PID $LISTING_PID $USER_PID $FEED_PID $DETAIL_PID 2>/dev/null || true
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Build all services
echo "🔨 Building vendor-service..."
cd services/vendor-service && go build -o ../../bin/vendor-service ./cmd && cd ../..

echo "🔨 Building listing-service..."
cd services/listing-service && go build -o ../../bin/listing-service ./cmd && cd ../..

echo "🔨 Building user-service..."
cd services/user-service && go build -o ../../bin/user-service ./cmd && cd ../..

echo "🔨 Building feed-service..."
cd services/feed-service && go build -o ../../bin/feed-service ./cmd && cd ../..

echo "🔨 Building detail-page-service..."
cd services/detail-page-service && go build -o ../../bin/detail-page-service ./cmd && cd ../..

echo "✅ All services built successfully!"

# Start all services in background with different ports
echo "🚀 Starting vendor-service on port 8080..."
DB_NAME=vendor_service PORT=8080 ./bin/vendor-service &
VENDOR_PID=$!

sleep 2

echo "🚀 Starting listing-service on port 8081..."
DB_NAME=listing_service PORT=8081 ./bin/listing-service &
LISTING_PID=$!

sleep 2

echo "🚀 Starting user-service on port 8082..."
DB_NAME=user_service PORT=8082 ./bin/user-service &
USER_PID=$!

sleep 2

echo "🚀 Starting feed-service on port 8083..."
DB_NAME=feed_service PORT=8083 ./bin/feed-service &
FEED_PID=$!

sleep 2

echo "🚀 Starting detail-page-service on port 8084..."
DB_NAME=detail_page_service PORT=8084 ./bin/detail-page-service &
DETAIL_PID=$!

echo ""
echo "🎉 All microservices are running!"
echo ""
echo "📋 Service URLs:"
echo "   🏢 Vendor Service:      http://localhost:8080"
echo "   📝 Listing Service:     http://localhost:8081"
echo "   👤 User Service:        http://localhost:8082"
echo "   📰 Feed Service:        http://localhost:8083"
echo "   📄 Detail Page Service: http://localhost:8084"
echo ""
echo "🔍 Health Check URLs:"
echo "   curl http://localhost:8080/health"
echo "   curl http://localhost:8081/health"
echo "   curl http://localhost:8082/health"
echo "   curl http://localhost:8083/health"
echo "   curl http://localhost:8084/health"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for background processes
wait $VENDOR_PID $LISTING_PID $USER_PID $FEED_PID $DETAIL_PID
