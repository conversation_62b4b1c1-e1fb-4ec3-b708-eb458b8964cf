#!/bin/bash

# Start all services using Docker Compose
echo "🚀 Starting all Travel Booking Platform services with Docker..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Check if docker-compose is available
if ! command -v docker-compose > /dev/null 2>&1; then
    echo "❌ docker-compose is not installed. Please install docker-compose first."
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    docker-compose down
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Stop existing services
echo "🧹 Stopping existing services..."
docker-compose down

# Build and start all services
echo "🔨 Building and starting all services..."
docker-compose up --build

# Note: This will run in foreground and show logs from all services
# To run in background, use: docker-compose up --build -d
