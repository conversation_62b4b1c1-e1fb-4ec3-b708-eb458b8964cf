#!/bin/bash

# Run integration tests for vendor service
echo "🧪 Running Vendor Service Integration Tests..."

# Check if <PERSON><PERSON> is available
if ! command -v podman > /dev/null 2>&1; then
    echo "❌ Podman is not installed. Please install <PERSON><PERSON> first."
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up test environment..."
    podman stop travel-booking-postgres 2>/dev/null || true
    podman rm travel-booking-postgres 2>/dev/null || true
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start PostgreSQL for testing
echo "🐘 Starting PostgreSQL for testing..."
podman stop travel-booking-postgres 2>/dev/null || true
podman rm travel-booking-postgres 2>/dev/null || true

podman run -d \
    --name travel-booking-postgres \
    -e POSTGRES_USER=postgres \
    -e POSTGRES_PASSWORD=postgres \
    -e POSTGRES_DB=postgres \
    -p 5432:5432 \
    postgres:15-alpine

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if podman exec travel-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting... ($i/30)"
    sleep 2
done

# Check if PostgreSQL is ready
if ! podman exec travel-booking-postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo "❌ PostgreSQL failed to start"
    exit 1
fi

# Create databases
echo "🏗️  Creating test databases..."
podman exec travel-booking-postgres psql -U postgres -c "CREATE DATABASE vendor_central;" 2>/dev/null || echo "Database already exists"

# Set test environment variables
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5432
export TEST_DB_USER=postgres
export TEST_DB_PASSWORD=postgres
export TEST_DB_NAME=vendor_central

# Navigate to vendor service directory
cd services/vendor-central/vendor

# Download test dependencies
echo "📦 Downloading test dependencies..."
go mod tidy

# Run integration tests
echo "🚀 Running integration tests..."
echo "================================"

# Run tests with verbose output
go test -v ./tests/... -timeout=30s

TEST_EXIT_CODE=$?

# Return to original directory
cd ../../..

# Print test results
echo ""
echo "================================"
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ All integration tests passed!"
else
    echo "❌ Some integration tests failed!"
fi

echo ""
echo "📊 Test Summary:"
echo "   Database: PostgreSQL (vendor_central)"
echo "   Service: Vendor Service"
echo "   Test Type: Integration Tests"
echo ""

# Exit with the test result code
exit $TEST_EXIT_CODE
