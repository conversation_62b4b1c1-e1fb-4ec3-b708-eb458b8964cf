#!/bin/bash

# Run integration tests for vendor service
echo "🧪 Running Vendor Service Integration Tests..."

# Check if Dock<PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "❌ Docker is not running. Please start Docker first."
    exit 1
fi

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🧹 Cleaning up test environment..."
    docker-compose down
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM EXIT

# Start PostgreSQL for testing
echo "🐘 Starting PostgreSQL for testing..."
docker-compose up -d postgres

# Wait for PostgreSQL to be ready
echo "⏳ Waiting for PostgreSQL to be ready..."
for i in {1..30}; do
    if docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
        echo "✅ PostgreSQL is ready!"
        break
    fi
    echo "⏳ Still waiting... ($i/30)"
    sleep 2
done

# Check if PostgreSQL is ready
if ! docker-compose exec postgres pg_isready -U postgres > /dev/null 2>&1; then
    echo "❌ PostgreSQL failed to start"
    exit 1
fi

# Set test environment variables
export TEST_DB_HOST=localhost
export TEST_DB_PORT=5432
export TEST_DB_USER=postgres
export TEST_DB_PASSWORD=postgres
export TEST_DB_NAME=vendor_central

# Navigate to vendor service directory
cd services/vendor-central/vendor

# Download test dependencies
echo "📦 Downloading test dependencies..."
go mod tidy

# Run integration tests
echo "🚀 Running integration tests..."
echo "================================"

# Run tests with verbose output
go test -v ./tests/... -timeout=30s

TEST_EXIT_CODE=$?

# Return to original directory
cd ../../..

# Print test results
echo ""
echo "================================"
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "✅ All integration tests passed!"
else
    echo "❌ Some integration tests failed!"
fi

echo ""
echo "📊 Test Summary:"
echo "   Database: PostgreSQL (vendor_central)"
echo "   Service: Vendor Service"
echo "   Test Type: Integration Tests"
echo ""

# Exit with the test result code
exit $TEST_EXIT_CODE
