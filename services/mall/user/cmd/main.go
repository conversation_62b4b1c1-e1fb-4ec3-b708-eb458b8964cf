package main

import (
"log"
"net/http"
"os"

"github.com/go-chi/chi/v5"
"github.com/go-chi/chi/v5/middleware"
)

func main() {
// Initialize logger
logger := log.New(os.Stdout, "USER: ", log.LstdFlags)
logger.Println("Starting User Service")

// Get port from environment or use default
port := os.Getenv("PORT")
if port == "" {
port = "8082"
}

// Setup router with middleware
r := chi.NewRouter()
r.Use(middleware.Logger)
r.Use(middleware.Recoverer)

// Health check endpoint
r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
w.WriteHeader(http.StatusOK)
w.Write([]byte("User Service OK"))
})

// Placeholder API routes
r.Route("/api/v1", func(r chi.Router) {
r.Post("/users/register", func(w http.ResponseWriter, r *http.Request) {
w.<PERSON><PERSON>().Set("Content-Type", "application/json")
w.<PERSON><PERSON>ead<PERSON>(http.StatusOK)
w.Write([]byte(`{"message": "User service - register endpoint (not implemented)"}`))
})
})

// Start server
logger.Printf("User Service listening on port %s", port)
if err := http.ListenAndServe(":"+port, r); err != nil {
logger.Fatalf("Server error: %v", err)
}
}
