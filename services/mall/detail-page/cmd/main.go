package main

import (
"log"
"net/http"
"os"

"github.com/go-chi/chi/v5"
"github.com/go-chi/chi/v5/middleware"
)

func main() {
// Initialize logger
logger := log.New(os.Stdout, "DETAIL-PAGE: ", log.LstdFlags)
logger.Println("Starting Detail Page Service")

// Get port from environment or use default
port := os.Getenv("PORT")
if port == "" {
port = "8084"
}

// Setup router with middleware
r := chi.NewRouter()
r.Use(middleware.Logger)
r.Use(middleware.Recoverer)

// Health check endpoint
r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
w.WriteHeader(http.StatusOK)
w.Write([]byte("Detail Page Service OK"))
})

// Placeholder API routes
r.Route("/api/v1", func(r chi.Router) {
r.Get("/trips/{tripId}", func(w http.ResponseWriter, r *http.Request) {
w.Header().Set("Content-Type", "application/json")
w.WriteHeader(http.StatusOK)
w.Write([]byte(`{"message": "Detail page service - trip details endpoint (not implemented)"}`))
})
})

// Start server
logger.Printf("Detail Page Service listening on port %s", port)
if err := http.ListenAndServe(":"+port, r); err != nil {
logger.Fatalf("Server error: %v", err)
}
}
