package repository

import (
	"context"
	"fmt"

	"github.com/google/uuid"
	"github.com/travel-booking/backend/services/vendor-service/internal/models"
	"gorm.io/gorm"
)

// VendorRepository defines the interface for vendor data access
type VendorRepository interface {
	// Create creates a new vendor
	Create(ctx context.Context, vendor *models.Vendor) error
	
	// GetByID retrieves a vendor by ID
	GetByID(ctx context.Context, vendorID uuid.UUID) (*models.Vendor, error)
	
	// GetByEmail retrieves a vendor by email
	GetByEmail(ctx context.Context, email string) (*models.Vendor, error)
	
	// Update updates a vendor
	Update(ctx context.Context, vendor *models.Vendor) error
	
	// ListByStatus lists vendors by status with pagination
	ListByStatus(ctx context.Context, status string, page, limit int) ([]*models.Vendor, int64, error)
	
	// EmailExists checks if email already exists
	EmailExists(ctx context.Context, email string) (bool, error)
}

// vendorRepository implements VendorRepository interface
type vendorRepository struct {
	db *gorm.DB
}

// Ensure vendorRepository implements VendorRepository interface
var _ VendorRepository = (*vendorRepository)(nil)

// NewVendorRepository creates a new vendor repository
func NewVendorRepository(db *gorm.DB) VendorRepository {
	return &vendorRepository{db: db}
}

// Create creates a new vendor
func (r *vendorRepository) Create(ctx context.Context, vendor *models.Vendor) error {
	if vendor.ID == uuid.Nil {
		vendor.ID = uuid.New()
	}
	
	if err := r.db.WithContext(ctx).Create(vendor).Error; err != nil {
		return fmt.Errorf("failed to create vendor: %w", err)
	}
	
	return nil
}

// GetByID retrieves a vendor by ID
func (r *vendorRepository) GetByID(ctx context.Context, vendorID uuid.UUID) (*models.Vendor, error) {
	var vendor models.Vendor
	if err := r.db.WithContext(ctx).First(&vendor, "id = ?", vendorID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("vendor not found")
		}
		return nil, fmt.Errorf("failed to get vendor: %w", err)
	}
	
	return &vendor, nil
}

// GetByEmail retrieves a vendor by email
func (r *vendorRepository) GetByEmail(ctx context.Context, email string) (*models.Vendor, error) {
	var vendor models.Vendor
	if err := r.db.WithContext(ctx).First(&vendor, "email = ?", email).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("vendor not found")
		}
		return nil, fmt.Errorf("failed to get vendor: %w", err)
	}
	
	return &vendor, nil
}

// Update updates a vendor
func (r *vendorRepository) Update(ctx context.Context, vendor *models.Vendor) error {
	if err := r.db.WithContext(ctx).Save(vendor).Error; err != nil {
		return fmt.Errorf("failed to update vendor: %w", err)
	}
	
	return nil
}

// ListByStatus lists vendors by status with pagination
func (r *vendorRepository) ListByStatus(ctx context.Context, status string, page, limit int) ([]*models.Vendor, int64, error) {
	var vendors []*models.Vendor
	var total int64
	
	offset := (page - 1) * limit
	
	// Count total records
	if err := r.db.WithContext(ctx).Model(&models.Vendor{}).Where("status = ?", status).Count(&total).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to count vendors: %w", err)
	}
	
	// Get paginated results
	if err := r.db.WithContext(ctx).Where("status = ?", status).
		Offset(offset).Limit(limit).Find(&vendors).Error; err != nil {
		return nil, 0, fmt.Errorf("failed to list vendors: %w", err)
	}
	
	return vendors, total, nil
}

// EmailExists checks if email already exists
func (r *vendorRepository) EmailExists(ctx context.Context, email string) (bool, error) {
	var count int64
	if err := r.db.WithContext(ctx).Model(&models.Vendor{}).Where("email = ?", email).Count(&count).Error; err != nil {
		return false, fmt.Errorf("failed to check email existence: %w", err)
	}
	
	return count > 0, nil
}
