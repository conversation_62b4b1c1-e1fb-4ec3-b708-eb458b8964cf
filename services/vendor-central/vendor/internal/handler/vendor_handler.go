package handler

import (
	"encoding/json"
	"net/http"
	"strconv"

	"github.com/go-chi/chi/v5"
	"github.com/google/uuid"
	"github.com/travel-booking/backend/services/vendor-service/internal/dto"
	"github.com/travel-booking/backend/services/vendor-service/internal/service"
	"github.com/travel-booking/backend/services/vendor-service/internal/utils"
)

// VendorHandler handles HTTP requests for vendor operations
type VendorHandler struct {
	service service.VendorService
}

// NewVendorHandler creates a new vendor handler
func NewVendorHandler(service service.VendorService) *VendorHandler {
	return &VendorHandler{
		service: service,
	}
}

// RegisterRoutes registers the vendor routes
func (h *VendorHandler) RegisterRoutes(r chi.Router) {
	r.Route("/vendors", func(r chi.Router) {
		r.Post("/register", h.RegisterVendor)
		r.Get("/{vendorId}", h.GetVendor)
		r.Get("/pending", h.ListPendingVendors)
		r.Put("/{vendorId}/verify", h.VerifyVendor)
		r.Put("/{vendorId}/reject", h.RejectVendor)
	})
}

// RegisterVendor handles vendor registration
func (h *VendorHandler) RegisterVendor(w http.ResponseWriter, r *http.Request) {
	var req struct {
		Email           string `json:"email"`
		Password        string `json:"password"`
		CompanyName     string `json:"company_name"`
		ContactName     string `json:"contact_name"`
		PhoneNumber     string `json:"phone_number"`
		AddressLine1    string `json:"address_line_1"`
		AddressLine2    string `json:"address_line_2"`
		City            string `json:"city"`
		District        string `json:"district"`
		Country         string `json:"country"`
		ZipCode         string `json:"zip_code"`
		BusinessLicense string `json:"business_license"`
		TaxID           string `json:"tax_id"`
		Website         string `json:"website"`
		Description     string `json:"description"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	// Basic validation
	if req.Email == "" || req.Password == "" || req.CompanyName == "" {
		utils.RespondWithError(w, http.StatusBadRequest, "Email, password, and company name are required")
		return
	}

	// Convert to registration request
	regReq := &dto.VendorRegistrationRequest{
		Email:           req.Email,
		Password:        req.Password,
		CompanyName:     req.CompanyName,
		ContactName:     req.ContactName,
		PhoneNumber:     req.PhoneNumber,
		AddressLine1:    req.AddressLine1,
		AddressLine2:    req.AddressLine2,
		City:            req.City,
		District:        req.District,
		Country:         req.Country,
		ZipCode:         req.ZipCode,
		BusinessLicense: req.BusinessLicense,
		TaxID:           req.TaxID,
		Website:         req.Website,
		Description:     req.Description,
	}

	vendor, err := h.service.RegisterVendor(r.Context(), regReq)
	if err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.RespondWithJSON(w, http.StatusCreated, vendor)
}

// GetVendor handles getting a vendor by ID
func (h *VendorHandler) GetVendor(w http.ResponseWriter, r *http.Request) {
	vendorIDStr := chi.URLParam(r, "vendorId")
	vendorID, err := uuid.Parse(vendorIDStr)
	if err != nil {
		utils.RespondWithError(w, http.StatusBadRequest, "Invalid vendor ID")
		return
	}

	vendor, err := h.service.GetVendorByID(r.Context(), vendorID)
	if err != nil {
		utils.RespondWithError(w, http.StatusNotFound, err.Error())
		return
	}

	utils.RespondWithJSON(w, http.StatusOK, vendor)
}

// ListPendingVendors handles listing pending vendor applications
func (h *VendorHandler) ListPendingVendors(w http.ResponseWriter, r *http.Request) {
	page, _ := strconv.Atoi(r.URL.Query().Get("page"))
	if page < 1 {
		page = 1
	}

	limit, _ := strconv.Atoi(r.URL.Query().Get("limit"))
	if limit < 1 || limit > 100 {
		limit = 20
	}

	vendors, total, err := h.service.ListPendingVendors(r.Context(), page, limit)
	if err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, err.Error())
		return
	}

	response := map[string]interface{}{
		"vendors": vendors,
		"pagination": map[string]interface{}{
			"page":  page,
			"limit": limit,
			"total": total,
		},
	}

	utils.RespondWithJSON(w, http.StatusOK, response)
}

// VerifyVendor handles vendor verification
func (h *VendorHandler) VerifyVendor(w http.ResponseWriter, r *http.Request) {
	vendorIDStr := chi.URLParam(r, "vendorId")
	vendorID, err := uuid.Parse(vendorIDStr)
	if err != nil {
		utils.RespondWithError(w, http.StatusBadRequest, "Invalid vendor ID")
		return
	}

	if err := h.service.VerifyVendor(r.Context(), vendorID); err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.RespondWithJSON(w, http.StatusOK, map[string]string{"message": "Vendor verified successfully"})
}

// RejectVendor handles vendor rejection
func (h *VendorHandler) RejectVendor(w http.ResponseWriter, r *http.Request) {
	vendorIDStr := chi.URLParam(r, "vendorId")
	vendorID, err := uuid.Parse(vendorIDStr)
	if err != nil {
		utils.RespondWithError(w, http.StatusBadRequest, "Invalid vendor ID")
		return
	}

	var req struct {
		Reason string `json:"reason"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		utils.RespondWithError(w, http.StatusBadRequest, "Invalid request body")
		return
	}

	if err := h.service.RejectVendor(r.Context(), vendorID, req.Reason); err != nil {
		utils.RespondWithError(w, http.StatusInternalServerError, err.Error())
		return
	}

	utils.RespondWithJSON(w, http.StatusOK, map[string]string{"message": "Vendor rejected successfully"})
}
