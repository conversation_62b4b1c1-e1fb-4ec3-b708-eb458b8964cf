package database

import (
	"fmt"

	"github.com/travel-booking/backend/services/vendor-service/internal/config"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

// Connection represents a database connection
type Connection struct {
	DB *gorm.DB
}

// NewConnection creates a new database connection
func NewConnection(cfg *config.Config) (*Connection, error) {
	db, err := gorm.Open(postgres.Open(cfg.GetDatabaseDSN()), &gorm.Config{})
	if err != nil {
		return nil, fmt.Errorf("failed to connect to database: %w", err)
	}

	return &Connection{DB: db}, nil
}

// Close closes the database connection
func (c *Connection) Close() error {
	sqlDB, err := c.DB.DB()
	if err != nil {
		return err
	}
	return sqlDB.Close()
}

// GetDB returns the GORM database instance
func (c *Connection) GetDB() *gorm.DB {
	return c.DB
}
