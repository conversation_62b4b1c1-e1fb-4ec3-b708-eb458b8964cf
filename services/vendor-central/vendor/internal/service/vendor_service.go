package service

import (
	"context"
	"fmt"
	"time"

	"github.com/google/uuid"
	"github.com/travel-booking/backend/services/vendor-service/internal/dto"
	"github.com/travel-booking/backend/services/vendor-service/internal/models"
	"github.com/travel-booking/backend/services/vendor-service/internal/repository"
	"golang.org/x/crypto/bcrypt"
)

// VendorService defines the interface for vendor operations
type VendorService interface {
	// RegisterVendor registers a new vendor
	RegisterVendor(ctx context.Context, req *dto.VendorRegistrationRequest) (*dto.VendorResponse, error)

	// GetVendorByID retrieves a vendor by ID
	GetVendorByID(ctx context.Context, vendorID uuid.UUID) (*dto.VendorResponse, error)

	// GetVendorByEmail retrieves a vendor by email
	GetVendorByEmail(ctx context.Context, email string) (*dto.VendorResponse, error)

	// UpdateVendorStatus updates vendor verification status
	UpdateVendorStatus(ctx context.Context, vendorID uuid.UUID, status string) error

	// ListPendingVendors lists all pending vendor applications
	ListPendingVendors(ctx context.Context, page, limit int) ([]*dto.VendorResponse, int64, error)

	// VerifyVendor marks a vendor as verified
	VerifyVendor(ctx context.Context, vendorID uuid.UUID) error

	// RejectVendor rejects a vendor application
	RejectVendor(ctx context.Context, vendorID uuid.UUID, reason string) error
}

// vendorService implements VendorService interface
type vendorService struct {
	vendorRepo repository.VendorRepository
}

// Ensure vendorService implements VendorService interface
var _ VendorService = (*vendorService)(nil)

// NewVendorService creates a new vendor service
func NewVendorService(vendorRepo repository.VendorRepository) VendorService {
	return &vendorService{
		vendorRepo: vendorRepo,
	}
}

// RegisterVendor registers a new vendor
func (s *vendorService) RegisterVendor(ctx context.Context, req *dto.VendorRegistrationRequest) (*dto.VendorResponse, error) {
	// Check if email already exists
	exists, err := s.vendorRepo.EmailExists(ctx, req.Email)
	if err != nil {
		return nil, fmt.Errorf("failed to check email existence: %w", err)
	}
	if exists {
		return nil, fmt.Errorf("email already exists")
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// Create vendor
	vendor := &models.Vendor{
		ID:              uuid.New(),
		Email:           req.Email,
		PasswordHash:    string(hashedPassword),
		CompanyName:     req.CompanyName,
		ContactName:     req.ContactName,
		PhoneNumber:     req.PhoneNumber,
		AddressLine1:    req.AddressLine1,
		AddressLine2:    req.AddressLine2,
		City:            req.City,
		District:        req.District,
		Country:         req.Country,
		ZipCode:         req.ZipCode,
		BusinessLicense: req.BusinessLicense,
		TaxID:           req.TaxID,
		Website:         req.Website,
		Description:     req.Description,
		IsVerified:      false,
		Status:          "pending",
		CreatedAt:       time.Now(),
		UpdatedAt:       time.Now(),
	}

	if err := s.vendorRepo.Create(ctx, vendor); err != nil {
		return nil, fmt.Errorf("failed to create vendor: %w", err)
	}

	return s.toVendorResponse(vendor), nil
}

// GetVendorByID retrieves a vendor by ID
func (s *vendorService) GetVendorByID(ctx context.Context, vendorID uuid.UUID) (*dto.VendorResponse, error) {
	vendor, err := s.vendorRepo.GetByID(ctx, vendorID)
	if err != nil {
		return nil, err
	}

	return s.toVendorResponse(vendor), nil
}

// GetVendorByEmail retrieves a vendor by email
func (s *vendorService) GetVendorByEmail(ctx context.Context, email string) (*dto.VendorResponse, error) {
	vendor, err := s.vendorRepo.GetByEmail(ctx, email)
	if err != nil {
		return nil, err
	}

	return s.toVendorResponse(vendor), nil
}

// UpdateVendorStatus updates vendor verification status
func (s *vendorService) UpdateVendorStatus(ctx context.Context, vendorID uuid.UUID, status string) error {
	vendor, err := s.vendorRepo.GetByID(ctx, vendorID)
	if err != nil {
		return err
	}

	vendor.Status = status
	vendor.UpdatedAt = time.Now()

	return s.vendorRepo.Update(ctx, vendor)
}

// ListPendingVendors lists all pending vendor applications
func (s *vendorService) ListPendingVendors(ctx context.Context, page, limit int) ([]*dto.VendorResponse, int64, error) {
	vendors, total, err := s.vendorRepo.ListByStatus(ctx, "pending", page, limit)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]*dto.VendorResponse, len(vendors))
	for i, vendor := range vendors {
		responses[i] = s.toVendorResponse(vendor)
	}

	return responses, total, nil
}

// VerifyVendor marks a vendor as verified
func (s *vendorService) VerifyVendor(ctx context.Context, vendorID uuid.UUID) error {
	vendor, err := s.vendorRepo.GetByID(ctx, vendorID)
	if err != nil {
		return err
	}

	vendor.Status = "approved"
	vendor.IsVerified = true
	vendor.UpdatedAt = time.Now()

	return s.vendorRepo.Update(ctx, vendor)
}

// RejectVendor rejects a vendor application
func (s *vendorService) RejectVendor(ctx context.Context, vendorID uuid.UUID, reason string) error {
	vendor, err := s.vendorRepo.GetByID(ctx, vendorID)
	if err != nil {
		return err
	}

	vendor.Status = "rejected"
	vendor.UpdatedAt = time.Now()

	return s.vendorRepo.Update(ctx, vendor)
}

// toVendorResponse converts a vendor model to response
func (s *vendorService) toVendorResponse(vendor *models.Vendor) *dto.VendorResponse {
	return &dto.VendorResponse{
		ID:              vendor.ID.String(),
		Email:           vendor.Email,
		CompanyName:     vendor.CompanyName,
		ContactName:     vendor.ContactName,
		PhoneNumber:     vendor.PhoneNumber,
		AddressLine1:    vendor.AddressLine1,
		AddressLine2:    vendor.AddressLine2,
		City:            vendor.City,
		District:        vendor.District,
		Country:         vendor.Country,
		ZipCode:         vendor.ZipCode,
		BusinessLicense: vendor.BusinessLicense,
		TaxID:           vendor.TaxID,
		Website:         vendor.Website,
		LogoImage:       vendor.LogoImage,
		Description:     vendor.Description,
		IsVerified:      vendor.IsVerified,
		Status:          vendor.Status,
		CreatedAt:       vendor.CreatedAt,
		UpdatedAt:       vendor.UpdatedAt,
	}
}
