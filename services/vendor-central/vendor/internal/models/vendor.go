package models

import (
	"time"

	"github.com/google/uuid"
)

// Vendor represents a vendor in the system
type Vendor struct {
	ID              uuid.UUID `json:"id" gorm:"primaryKey;type:uuid"`
	Email           string    `json:"email" gorm:"not null;unique"`
	PasswordHash    string    `json:"-" gorm:"not null"`
	CompanyName     string    `json:"company_name"`
	ContactName     string    `json:"contact_name"`
	PhoneNumber     string    `json:"phone_number" gorm:"not null"`
	AddressLine1    string    `json:"address_line_1"`
	AddressLine2    string    `json:"address_line_2"`
	City            string    `json:"city"`
	District        string    `json:"district"`
	Country         string    `json:"country"`
	ZipCode         string    `json:"zip_code"`
	BusinessLicense string    `json:"business_license"`
	TaxID           string    `json:"tax_id"`
	Website         string    `json:"website"`
	LogoImage       string    `json:"logo_image"`
	Description     string    `json:"description"`
	IsVerified      bool      `json:"is_verified" gorm:"default:false"`
	Status          string    `json:"status" gorm:"default:pending"` // pending, approved, rejected
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}
