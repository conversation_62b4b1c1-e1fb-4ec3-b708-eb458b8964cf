package tests

import (
	"context"
	"fmt"
	"log"
	"os"
	"testing"
	"time"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	"github.com/travel-booking/backend/services/vendor-service/internal/models"
)

// TestDatabaseManager manages test database lifecycle
type TestDatabaseManager struct {
	db *gorm.DB
}

// NewTestDatabaseManager creates a new test database manager
func NewTestDatabaseManager() *TestDatabaseManager {
	return &TestDatabaseManager{}
}

// Setup initializes the test database
func (tdm *TestDatabaseManager) Setup() error {
	dsn := getTestDatabaseDSN()
	
	// Configure GORM with silent logger for tests
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		return fmt.Errorf("failed to connect to test database: %w", err)
	}

	tdm.db = db

	// Auto-migrate the schema
	if err := db.AutoMigrate(&models.Vendor{}); err != nil {
		return fmt.Errorf("failed to migrate test database: %w", err)
	}

	return nil
}

// Cleanup cleans up the test database
func (tdm *TestDatabaseManager) Cleanup() error {
	if tdm.db != nil {
		// Clean up all test data
		if err := tdm.db.Exec("DELETE FROM vendors").Error; err != nil {
			return fmt.Errorf("failed to clean up test data: %w", err)
		}

		// Close the database connection
		sqlDB, err := tdm.db.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB returns the test database instance
func (tdm *TestDatabaseManager) GetDB() *gorm.DB {
	return tdm.db
}

// WaitForDatabase waits for the database to be ready
func WaitForDatabase(dsn string, timeout time.Duration) error {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("timeout waiting for database to be ready")
		case <-ticker.C:
			db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
				Logger: logger.Default.LogMode(logger.Silent),
			})
			if err == nil {
				sqlDB, err := db.DB()
				if err == nil {
					if err := sqlDB.Ping(); err == nil {
						sqlDB.Close()
						return nil
					}
					sqlDB.Close()
				}
			}
		}
	}
}

// SetupTestEnvironment sets up the test environment
func SetupTestEnvironment() error {
	// Set test environment variables if not already set
	if os.Getenv("TEST_DB_HOST") == "" {
		os.Setenv("TEST_DB_HOST", "localhost")
	}
	if os.Getenv("TEST_DB_PORT") == "" {
		os.Setenv("TEST_DB_PORT", "5432")
	}
	if os.Getenv("TEST_DB_USER") == "" {
		os.Setenv("TEST_DB_USER", "postgres")
	}
	if os.Getenv("TEST_DB_PASSWORD") == "" {
		os.Setenv("TEST_DB_PASSWORD", "postgres")
	}
	if os.Getenv("TEST_DB_NAME") == "" {
		os.Setenv("TEST_DB_NAME", "vendor_central")
	}

	// Wait for database to be ready
	dsn := getTestDatabaseDSN()
	if err := WaitForDatabase(dsn, 30*time.Second); err != nil {
		return fmt.Errorf("test database is not ready: %w", err)
	}

	log.Println("Test environment setup complete")
	return nil
}

// TestMain sets up and tears down the test environment
func TestMain(m *testing.M) {
	// Setup test environment
	if err := SetupTestEnvironment(); err != nil {
		log.Fatalf("Failed to setup test environment: %v", err)
	}

	// Run tests
	code := m.Run()

	// Exit with the test result code
	os.Exit(code)
}
