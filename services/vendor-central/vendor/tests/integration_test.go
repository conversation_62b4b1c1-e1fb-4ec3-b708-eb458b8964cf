package tests

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"
	"time"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"

	"github.com/travel-booking/backend/services/vendor-service/internal/dto"
	"github.com/travel-booking/backend/services/vendor-service/internal/handler"
	"github.com/travel-booking/backend/services/vendor-service/internal/models"
	"github.com/travel-booking/backend/services/vendor-service/internal/repository"
	"github.com/travel-booking/backend/services/vendor-service/internal/service"
)

// VendorIntegrationTestSuite defines the test suite
type VendorIntegrationTestSuite struct {
	suite.Suite
	db     *gorm.DB
	router *chi.Mux
	server *httptest.Server
}

// SetupSuite runs once before all tests
func (suite *VendorIntegrationTestSuite) SetupSuite() {
	// Setup test database connection
	dsn := getTestDatabaseDSN()
	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	require.NoError(suite.T(), err)

	suite.db = db

	// Auto-migrate the schema
	err = db.AutoMigrate(&models.Vendor{})
	require.NoError(suite.T(), err)

	// Setup dependencies
	vendorRepo := repository.NewVendorRepository(db)
	vendorService := service.NewVendorService(vendorRepo)
	vendorHandler := handler.NewVendorHandler(vendorService)

	// Setup router
	r := chi.NewRouter()
	r.Use(middleware.Logger)
	r.Use(middleware.Recoverer)
	r.Use(middleware.Timeout(60 * time.Second))

	// Register routes
	r.Route("/api/v1", func(r chi.Router) {
		vendorHandler.RegisterRoutes(r)
	})

	// Health check endpoint
	r.Get("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	suite.router = r
	suite.server = httptest.NewServer(r)
}

// TearDownSuite runs once after all tests
func (suite *VendorIntegrationTestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
}

// SetupTest runs before each test
func (suite *VendorIntegrationTestSuite) SetupTest() {
	// Clean up the database before each test
	suite.db.Exec("DELETE FROM vendors")
}

// getTestDatabaseDSN returns the test database connection string
func getTestDatabaseDSN() string {
	host := getEnvOrDefault("TEST_DB_HOST", "localhost")
	port := getEnvOrDefault("TEST_DB_PORT", "5432")
	user := getEnvOrDefault("TEST_DB_USER", "postgres")
	password := getEnvOrDefault("TEST_DB_PASSWORD", "postgres")
	dbname := getEnvOrDefault("TEST_DB_NAME", "vendor_central")

	return fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=disable",
		host, port, user, password, dbname)
}

// getEnvOrDefault returns the value of the environment variable or the default value
func getEnvOrDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// TestHealthCheck tests the health check endpoint
func (suite *VendorIntegrationTestSuite) TestHealthCheck() {
	resp, err := http.Get(suite.server.URL + "/health")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
}

// TestVendorRegistration tests vendor registration
func (suite *VendorIntegrationTestSuite) TestVendorRegistration() {
	// Test data
	vendorData := dto.VendorRegistrationRequest{
		Email:        "<EMAIL>",
		Password:     "password123",
		CompanyName:  "Test Travel Company",
		ContactName:  "John Doe",
		PhoneNumber:  "+**********",
		AddressLine1: "123 Main St",
		City:         "New York",
		Country:      "USA",
		Description:  "A test travel company",
	}

	// Convert to JSON
	jsonData, err := json.Marshal(vendorData)
	require.NoError(suite.T(), err)

	// Make request
	resp, err := http.Post(
		suite.server.URL+"/api/v1/vendors/register",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	// Check response
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	// Parse response
	var vendorResponse dto.VendorResponse
	err = json.NewDecoder(resp.Body).Decode(&vendorResponse)
	require.NoError(suite.T(), err)

	// Validate response
	assert.NotEmpty(suite.T(), vendorResponse.ID)
	assert.Equal(suite.T(), vendorData.Email, vendorResponse.Email)
	assert.Equal(suite.T(), vendorData.CompanyName, vendorResponse.CompanyName)
	assert.Equal(suite.T(), vendorData.ContactName, vendorResponse.ContactName)
	assert.Equal(suite.T(), "pending", vendorResponse.Status)
	assert.False(suite.T(), vendorResponse.IsVerified)
}

// TestVendorRegistrationDuplicateEmail tests duplicate email registration
func (suite *VendorIntegrationTestSuite) TestVendorRegistrationDuplicateEmail() {
	// First registration
	vendorData := dto.VendorRegistrationRequest{
		Email:        "<EMAIL>",
		Password:     "password123",
		CompanyName:  "First Company",
		ContactName:  "John Doe",
		PhoneNumber:  "+**********",
		AddressLine1: "123 Main St",
		City:         "New York",
		Country:      "USA",
	}

	jsonData, err := json.Marshal(vendorData)
	require.NoError(suite.T(), err)

	// First request should succeed
	resp, err := http.Post(
		suite.server.URL+"/api/v1/vendors/register",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	require.NoError(suite.T(), err)
	resp.Body.Close()
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)

	// Second request with same email should fail
	vendorData.CompanyName = "Second Company"
	jsonData, err = json.Marshal(vendorData)
	require.NoError(suite.T(), err)

	resp, err = http.Post(
		suite.server.URL+"/api/v1/vendors/register",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusInternalServerError, resp.StatusCode)
}

// TestGetVendorByID tests getting a vendor by ID
func (suite *VendorIntegrationTestSuite) TestGetVendorByID() {
	// Create a vendor first
	vendor := &models.Vendor{
		ID:           uuid.New(),
		Email:        "<EMAIL>",
		PasswordHash: "hashedpassword",
		CompanyName:  "Get Test Company",
		ContactName:  "Jane Doe",
		PhoneNumber:  "+**********",
		Status:       "pending",
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	err := suite.db.Create(vendor).Error
	require.NoError(suite.T(), err)

	// Make request
	resp, err := http.Get(suite.server.URL + "/api/v1/vendors/" + vendor.ID.String())
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	// Check response
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	// Parse response
	var vendorResponse dto.VendorResponse
	err = json.NewDecoder(resp.Body).Decode(&vendorResponse)
	require.NoError(suite.T(), err)

	// Validate response
	assert.Equal(suite.T(), vendor.ID.String(), vendorResponse.ID)
	assert.Equal(suite.T(), vendor.Email, vendorResponse.Email)
	assert.Equal(suite.T(), vendor.CompanyName, vendorResponse.CompanyName)
}

// TestGetVendorByIDNotFound tests getting a non-existent vendor
func (suite *VendorIntegrationTestSuite) TestGetVendorByIDNotFound() {
	nonExistentID := uuid.New()

	resp, err := http.Get(suite.server.URL + "/api/v1/vendors/" + nonExistentID.String())
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	assert.Equal(suite.T(), http.StatusNotFound, resp.StatusCode)
}

// TestListPendingVendors tests listing pending vendors
func (suite *VendorIntegrationTestSuite) TestListPendingVendors() {
	// Create test vendors
	vendors := []*models.Vendor{
		{
			ID:           uuid.New(),
			Email:        "<EMAIL>",
			PasswordHash: "hash1",
			CompanyName:  "Pending Company 1",
			Status:       "pending",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
		{
			ID:           uuid.New(),
			Email:        "<EMAIL>",
			PasswordHash: "hash2",
			CompanyName:  "Pending Company 2",
			Status:       "pending",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
		{
			ID:           uuid.New(),
			Email:        "<EMAIL>",
			PasswordHash: "hash3",
			CompanyName:  "Approved Company",
			Status:       "approved",
			CreatedAt:    time.Now(),
			UpdatedAt:    time.Now(),
		},
	}

	for _, vendor := range vendors {
		err := suite.db.Create(vendor).Error
		require.NoError(suite.T(), err)
	}

	// Make request
	resp, err := http.Get(suite.server.URL + "/api/v1/vendors/pending")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()

	// Check response
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)

	// Parse response
	var response map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&response)
	require.NoError(suite.T(), err)

	// Validate response
	vendors_list, ok := response["vendors"].([]interface{})
	assert.True(suite.T(), ok)
	assert.Len(suite.T(), vendors_list, 2) // Only pending vendors should be returned
}

// TestInvalidRequests tests various invalid request scenarios
func (suite *VendorIntegrationTestSuite) TestInvalidRequests() {
	// Test invalid JSON
	resp, err := http.Post(
		suite.server.URL+"/api/v1/vendors/register",
		"application/json",
		bytes.NewBuffer([]byte("invalid json")),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	assert.Equal(suite.T(), http.StatusBadRequest, resp.StatusCode)

	// Test missing required fields
	incompleteData := map[string]string{
		"email": "<EMAIL>",
		// Missing password, company_name, etc.
	}
	jsonData, _ := json.Marshal(incompleteData)

	resp, err = http.Post(
		suite.server.URL+"/api/v1/vendors/register",
		"application/json",
		bytes.NewBuffer(jsonData),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	assert.Equal(suite.T(), http.StatusBadRequest, resp.StatusCode)

	// Test invalid UUID in GET request
	resp, err = http.Get(suite.server.URL + "/api/v1/vendors/invalid-uuid")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	assert.Equal(suite.T(), http.StatusBadRequest, resp.StatusCode)
}

// TestVendorIntegration runs the integration test suite
func TestVendorIntegration(t *testing.T) {
	suite.Run(t, new(VendorIntegrationTestSuite))
}
