# Travel Booking Platform - Microservices Architecture

This document describes the microservices architecture of the travel booking platform backend, designed with clean architecture principles and complete service independence.

## Architecture Overview

The backend is split into 5 independent microservices:

### B2B Services
1. **Vendor Service** - Vendor registration and management
2. **Listing Service** - Trip listing and availability management

### B2C Services
3. **User Service** - User registration and authentication
4. **Feed Service** - Trip recommendations and search
5. **Detail Page Service** - Trip details, ratings, and bookings

Each service is completely independent with its own database, configuration, and deployment lifecycle.

## Directory Structure

```
backend/
├── services/                    # Independent microservices
│   ├── vendor-central/          # 🏢 B2B Services
│   │   ├── vendor/              # Vendor registration and management
│   │   │   ├── cmd/main.go      # Service entry point
│   │   │   ├── internal/        # Internal packages
│   │   │   │   ├── config/      # Configuration
│   │   │   │   ├── database/    # Database connection
│   │   │   │   ├── handler/     # HTTP handlers
│   │   │   │   ├── service/     # Business logic
│   │   │   │   ├── repository/  # Data access
│   │   │   │   ├── models/      # Domain models
│   │   │   │   └── utils/       # Utilities
│   │   │   └── go.mod           # Service dependencies
│   │   └── listing/             # Trip listing and availability
│   │       ├── cmd/main.go
│   │       └── go.mod
│   └── mall/                    # 👥 B2C Services
│       ├── user/                # User registration and auth
│       │   ├── cmd/main.go
│       │   └── go.mod
│       ├── feed/                # Trip recommendations and search
│       │   ├── cmd/main.go
│       │   └── go.mod
│       └── detail-page/         # Trip details, ratings, bookings
│           ├── cmd/main.go
│           └── go.mod
├── bin/                         # Built executables
├── scripts/                     # Deployment and utility scripts
│   └── run-services.sh          # Run all services for development
└── docs/                        # Documentation
    ├── api/                     # API documentation per service
    └── deployment/              # Deployment guides
```

## Clean Architecture Principles

Each module follows clean architecture with these layers:

### 1. Interface Layer (`interface/`)
- **Purpose**: Provides API interfaces to other modules and external clients
- **Contains**: HTTP handlers, service interfaces, DTOs
- **Dependencies**: Only depends on business layer
- **Rules**: No direct database access, no business logic

### 2. Business Layer (`business/`)
- **Purpose**: Contains all business logic and use cases
- **Contains**: Service implementations, business rules, validation
- **Dependencies**: Depends on repository interfaces (not implementations)
- **Rules**: No knowledge of HTTP, database specifics, or external services

### 3. Repository Layer (`repository/`)
- **Purpose**: Handles data access and persistence
- **Contains**: Database queries, data mapping, repository implementations
- **Dependencies**: Depends on database and models
- **Rules**: No business logic, only data access

### 4. Models Layer (`models/`)
- **Purpose**: Defines data structures and entities
- **Contains**: Domain models, DTOs, database entities
- **Dependencies**: No dependencies on other layers
- **Rules**: Pure data structures

## Module Descriptions

### Vendor Central (B2B)

#### Vendor Onboarding Module
- **Purpose**: Handle vendor registration and verification
- **Features**:
  - Vendor registration
  - Document verification
  - Approval workflow
  - Vendor profile management

#### Listing Module
- **Purpose**: Manage vendor trip listings
- **Features**:
  - Trip creation and management
  - Availability management
  - Pricing management
  - Trip status control

### Mall (B2C)

#### User Registration Module
- **Purpose**: Handle customer registration and authentication
- **Features**:
  - User registration
  - Authentication
  - Profile management
  - Password management

#### Feed Module
- **Purpose**: Provide personalized trip recommendations
- **Features**:
  - Personalized recommendations
  - Trip search and filtering
  - Trending trips
  - User activity tracking

#### Detail Page Module
- **Purpose**: Provide detailed trip information and booking
- **Features**:
  - Trip detail display
  - Rating and review system
  - Booking management
  - Availability checking

## Inter-Module Communication

### Rules
1. **No Direct Database Access**: Modules cannot access other modules' databases directly
2. **Interface-Only Communication**: All communication must go through defined interfaces
3. **No Shared Models**: Each module has its own models; use DTOs for communication
4. **Async Communication**: Use events/messaging for non-critical inter-module communication

### Communication Patterns
1. **HTTP APIs**: For synchronous communication between services
2. **Database Events**: For real-time data synchronization (PostgreSQL LISTEN/NOTIFY)
3. **Message Queues**: For asynchronous processing (future implementation)

## Database Setup

The platform uses **PostgreSQL** with two databases:
- `vendor_central` - For vendor and listing services
- `mall` - For user, feed, and detail-page services

### Quick Setup
```bash
# Complete development environment setup
./scripts/setup-development.sh

# Or just start the database
docker run -d --name travel-booking-postgres \
  -e POSTGRES_USER=postgres -e POSTGRES_PASSWORD=postgres \
  -p 5432:5432 \
  -v "$(pwd)/scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql" \
  postgres:15-alpine
```

### Testing Database Connection
```bash
# Test database setup
./scripts/test-db-connection.sh

# Test vendor service with database
./scripts/test-vendor-service.sh

# Test API endpoints
./scripts/test-api.sh
```

For detailed database setup instructions, see [DATABASE_SETUP.md](DATABASE_SETUP.md).

## Running the Microservices

### All Services (Development)
```bash
cd backend
./scripts/run-services.sh
```

### Individual Services
```bash
# Vendor Service (Port 8080)
cd services/vendor-central/vendor && go run cmd/main.go

# Listing Service (Port 8081)
cd services/vendor-central/listing && go run cmd/main.go

# User Service (Port 8082)
cd services/mall/user && go run cmd/main.go

# Feed Service (Port 8083)
cd services/mall/feed && go run cmd/main.go

# Detail Page Service (Port 8084)
cd services/mall/detail-page && go run cmd/main.go
```

## Environment Variables

Each service has its own configuration. Common variables:

```bash
# Database Configuration (per service)
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=vendor_central  # for vendor and listing services
DB_NAME=mall           # for user, feed, and detail-page services

# Service Ports
PORT=<service_port>  # 8080-8084

# Service-specific configurations
# Feed Service
ES_HOST=localhost
ES_PORT=9200

# User Service
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRY=24h
```

### Service Ports
- **Vendor Service**: 8080
- **Listing Service**: 8081
- **User Service**: 8082
- **Feed Service**: 8083
- **Detail Page Service**: 8084

## Database Schema

The platform uses two PostgreSQL databases:

### Vendor Central Database (`vendor_central`)
**Used by:** Vendor Service, Listing Service
- `vendors` - Vendor registration and profile data
- `trips` - Trip information and details
- `trip_images` - Trip photos and media
- `trip_availabilities` - Available dates and booking slots

### Mall Database (`mall`)
**Used by:** User Service, Feed Service, Detail Page Service
- `users` - User registration and profile data
- `user_preferences` - User recommendation preferences
- `user_activities` - User activity tracking for recommendations
- `ratings` - Trip ratings and reviews
- `bookings` - Trip bookings and reservations

## Development Guidelines

### Adding New Features
1. Identify which module the feature belongs to
2. Define interfaces first
3. Implement business logic
4. Add repository layer
5. Create HTTP handlers
6. Update module initialization

### Testing Strategy
1. **Unit Tests**: Test business logic in isolation
2. **Integration Tests**: Test repository layer with database
3. **API Tests**: Test HTTP handlers
4. **Contract Tests**: Test inter-module communication

### Code Organization
1. Keep modules independent
2. Use dependency injection
3. Define clear interfaces
4. Minimize external dependencies
5. Follow Go conventions

## Migration from Old Architecture

The old monolithic structure has been refactored into this modular design. Key changes:

1. **Separated Services**: Split into vendor-central and mall services
2. **Module Isolation**: Each module is self-contained
3. **Clean Interfaces**: Defined clear boundaries between modules
4. **Shared Infrastructure**: Common utilities moved to shared package
5. **Database Separation**: Each module owns its data

## Future Enhancements

1. **Service Mesh**: Implement service discovery and load balancing
2. **Event Sourcing**: Add event-driven architecture
3. **CQRS**: Separate read and write models
4. **Microservices**: Further split modules into separate services
5. **API Gateway**: Add centralized API management
