version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    container_name: travel-booking-postgres
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    networks:
      - travel-booking-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Vendor Central Services
  vendor-service:
    build:
      context: .
      dockerfile: services/vendor-central/vendor/Dockerfile
    container_name: vendor-service
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: vendor_central
      PORT: 8080
    ports:
      - "8080:8080"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - travel-booking-network

  listing-service:
    build:
      context: .
      dockerfile: services/vendor-central/listing/Dockerfile
    container_name: listing-service
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: vendor_central
      PORT: 8081
    ports:
      - "8081:8081"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - travel-booking-network

  # Mall Services
  user-service:
    build:
      context: .
      dockerfile: services/mall/user/Dockerfile
    container_name: user-service
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: mall
      PORT: 8082
    ports:
      - "8082:8082"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - travel-booking-network

  feed-service:
    build:
      context: .
      dockerfile: services/mall/feed/Dockerfile
    container_name: feed-service
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: mall
      PORT: 8083
    ports:
      - "8083:8083"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - travel-booking-network

  detail-page-service:
    build:
      context: .
      dockerfile: services/mall/detail-page/Dockerfile
    container_name: detail-page-service
    restart: unless-stopped
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: mall
      PORT: 8084
    ports:
      - "8084:8084"
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - travel-booking-network

volumes:
  postgres_data:

networks:
  travel-booking-network:
    driver: bridge